import { YDP_TAGS, YDAP_ACTION_TYPES } from '../../config/enum/index.js';
import { singleObj2Array } from '../utils.js';
import extractSQLActions from './sql';

/**
 * 根据iframeisret标识判断是否需要弹出alert提示
 */
const handleNoDataMessage = (isAlert, alertMsg, api) => {
  if (isAlert === 'false') return null;
  return {
    type: YDAP_ACTION_TYPES.ALERT,
    title: alertMsg,
    cond: `Object.keys(%a.${api}.business).length === 0`
  }
};

const extractIframeActions = async (ydpJson, pageName) => {
  const iframeArr = singleObj2Array(ydpJson?.[YDP_TAGS]);
  const actions = await iframeArr.reduce(async (acts, iframe) => {
    const {
      '@_triggername': triggerName, // 触发隐式提交的字段名称
      '@_value': num, // 隐式提交序号，用于生成js函数参数，通常在js中这样调用：iframesubmitEx(1)
      '@_sql': sql,
      '@_datasourcename': sourceName,
      '@_iframeisret': iframeIsAlert,
      '@_iframeretmes': iframeAlertMsg,
    } = iframe;
    const actionKey = `${triggerName}_${num}`;
    console.log(ydpJson, pageName);

    // 提取sql中的信息并转换成动作
    const sqlActionObj = await extractSQLActions(sql, actionKey);
    // 隐式提交相关的逻辑都要基于sql，如果sql转换失败，那么不继续进行后续转换
    if (!sqlActionObj) return acts;

    // 根据sql生成的动作
    const sqlAction = sqlActionObj.action;
    const { api: sqlActioAPI } = sqlAction;
    const action = [sqlAction];

    // 判断是否需要生成alert动作
    const alertAction = handleNoDataMessage(iframeIsAlert, iframeAlertMsg, sqlActioAPI);
    if (alertAction) action.push(alertAction);

    // 每个iframe标签生成一组动作
    acts[actionKey] = {
      ...sqlActionObj,
      action,
      // 动作以外需要保存到excel的信息
      sourceName,
    };

    return acts;
  }, {});

  return actions;
};

export default extractIframeActions;
